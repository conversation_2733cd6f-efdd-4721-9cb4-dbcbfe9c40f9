#!/usr/bin/env node

import React, { useState, useCallback } from 'react';
import { render, Box, Text, useApp, useInput } from 'ink';
import TextInput from 'ink-text-input';

// Handle errors gracefully
process.on('uncaughtException', (error: Error): void => {
  console.error('Error:', error);
  process.exit(1);
});

// Header Component
interface HeaderProps {
  title?: string;
  subtitle?: string;
}

const Header: React.FC<HeaderProps> = ({
  title = '🤖 AgentZero CLI',
  subtitle = 'Press Ctrl+C to exit'
}) => {
  return React.createElement(Box, { flexDirection: 'column', marginBottom: 1 },
    React.createElement(Text, { bold: true, color: 'cyan' }, title),
    React.createElement(Text, { dimColor: true }, subtitle)
  );
};

// InputBox Component
interface InputBoxProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: (value: string) => void;
  placeholder?: string;
  label?: string;
}

const InputBox: React.FC<InputBoxProps> = ({
  value,
  onChange,
  onSubmit,
  placeholder = 'Type your message or @path/to/file',
  label = 'Input'
}) => {
  return React.createElement(Box, { flexDirection: 'column', marginY: 1 },
    React.createElement(Box, {
      borderStyle: 'round',
      borderColor: 'cyan',
      paddingX: 1,
      paddingY: 0
    },
      React.createElement(Box, { flexDirection: 'column', width: '100%' },
        React.createElement(Text, { color: 'cyan', bold: true },
          `─ ${label} ${'─'.repeat(Math.max(0, 70 - label.length - 3))}`
        ),
        React.createElement(Box, {},
          React.createElement(Text, { color: 'cyan' }, '> '),
          React.createElement(TextInput, {
            value,
            onChange,
            onSubmit,
            placeholder
          })
        )
      )
    )
  );
};

// OutputBox Component
interface OutputBoxProps {
  content: string;
  label?: string;
  color?: string;
  isVisible?: boolean;
}

const OutputBox: React.FC<OutputBoxProps> = ({
  content,
  label = 'Output',
  color = 'green',
  isVisible = true
}) => {
  if (!isVisible || !content.trim()) {
    return null;
  }

  return React.createElement(Box, { flexDirection: 'column', marginY: 1 },
    React.createElement(Box, {
      borderStyle: 'round',
      borderColor: color,
      paddingX: 1,
      paddingY: 0
    },
      React.createElement(Box, { flexDirection: 'column', width: '100%' },
        React.createElement(Text, { color: color, bold: true },
          `─ ${label} ${'─'.repeat(Math.max(0, 70 - label.length - 3))}`
        ),
        React.createElement(Box, { paddingY: 1 },
          React.createElement(Text, {}, content)
        )
      )
    )
  );
};

// Main App Component
interface AppState {
  currentInput: string;
  lastOutput: string;
  isProcessing: boolean;
}

const App: React.FC = () => {
  const { exit } = useApp();
  const [state, setState] = useState<AppState>({
    currentInput: '',
    lastOutput: '',
    isProcessing: false
  });

  const handleInputChange = useCallback((value: string) => {
    setState(prev => ({ ...prev, currentInput: value }));
  }, []);

  const processCommand = useCallback((command: string): string => {
    const trimmedCommand = command.trim();

    if (trimmedCommand === 'exit' || trimmedCommand === 'quit') {
      exit();
      return '';
    }

    if (trimmedCommand === 'clear') {
      setState(prev => ({
        ...prev,
        lastOutput: '',
        currentInput: ''
      }));
      return '';
    }

    if (trimmedCommand === 'help') {
      return `Available commands:
• exit, quit - Exit the application
• clear - Clear the output
• help - Show this help message
• Any other text will be echoed back`;
    }

    // Echo the command back for now
    return `Echo: ${trimmedCommand}`;
  }, [exit]);

  const handleSubmit = useCallback((value: string) => {
    if (!value.trim()) {
      return;
    }

    setState(prev => ({ ...prev, isProcessing: true }));

    // Simulate processing
    setTimeout(() => {
      const output = processCommand(value);
      setState(() => ({
        currentInput: '',
        lastOutput: output,
        isProcessing: false
      }));
    }, 100);
  }, [processCommand]);

  // Handle Ctrl+C
  useInput((input, key) => {
    if (key.ctrl && input === 'c') {
      exit();
    }
  });

  return React.createElement(Box, { flexDirection: 'column', padding: 1 },
    React.createElement(Header),
    React.createElement(InputBox, {
      value: state.currentInput,
      onChange: handleInputChange,
      onSubmit: handleSubmit,
      placeholder: 'Type your message or @path/to/file'
    }),
    React.createElement(OutputBox, {
      content: state.lastOutput,
      isVisible: !!state.lastOutput
    }),
    state.isProcessing && React.createElement(Box, { marginY: 1 },
      React.createElement(Text, { dimColor: true }, 'Processing...')
    )
  );
};

// Render the app
render(React.createElement(App));
