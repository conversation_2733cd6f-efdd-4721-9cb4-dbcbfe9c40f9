import React from 'react';
import { Box, Text } from 'ink';

interface HeaderProps {
  title?: string;
  subtitle?: string;
}

export const Header: React.FC<HeaderProps> = ({ 
  title = '🤖 AgentZero CLI', 
  subtitle = 'Press Ctrl+C to exit' 
}) => {
  return (
    <Box flexDirection="column" marginBottom={1}>
      <Text bold color="cyan">
        {title}
      </Text>
      <Text dimColor>
        {subtitle}
      </Text>
    </Box>
  );
};
