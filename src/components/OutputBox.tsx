import React from 'react';
import { Box, Text } from 'ink';

interface OutputBoxProps {
  content: string;
  label?: string;
  color?: string;
  isVisible?: boolean;
}

export const OutputBox: React.FC<OutputBoxProps> = ({
  content,
  label = 'Output',
  color = 'green',
  isVisible = true
}) => {
  if (!isVisible || !content.trim()) {
    return null;
  }

  return (
    <Box flexDirection="column" marginY={1}>
      <Box borderStyle="round" borderColor={color} paddingX={1} paddingY={0}>
        <Box flexDirection="column" width="100%">
          <Text color={color} bold>
            ─ {label} {'─'.repeat(Math.max(0, 70 - label.length - 3))}
          </Text>
          <Box paddingY={1}>
            <Text>{content}</Text>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};
