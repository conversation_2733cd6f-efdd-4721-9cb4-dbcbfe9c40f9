import React, { useState, useCallback } from 'react';
import { Box, useApp } from 'ink';
import { Header } from './Header.js';
import { InputBox } from './InputBox.js';
import { OutputBox } from './OutputBox.js';

interface AppState {
  currentInput: string;
  lastOutput: string;
  isProcessing: boolean;
}

export const App: React.FC = () => {
  const { exit } = useApp();
  const [state, setState] = useState<AppState>({
    currentInput: '',
    lastOutput: '',
    isProcessing: false
  });

  const handleInputChange = useCallback((value: string) => {
    setState(prev => ({ ...prev, currentInput: value }));
  }, []);

  const processCommand = useCallback((command: string): string => {
    const trimmedCommand = command.trim();
    
    if (trimmedCommand === 'exit' || trimmedCommand === 'quit') {
      exit();
      return '';
    }

    if (trimmedCommand === 'clear') {
      setState(prev => ({ 
        ...prev, 
        lastOutput: '',
        currentInput: ''
      }));
      return '';
    }

    if (trimmedCommand === 'help') {
      return `Available commands:
• exit, quit - Exit the application
• clear - Clear the output
• help - Show this help message
• Any other text will be echoed back`;
    }

    // Echo the command back for now
    return `Echo: ${trimmedCommand}`;
  }, [exit]);

  const handleSubmit = useCallback((value: string) => {
    if (!value.trim()) {
      return;
    }

    setState(prev => ({ ...prev, isProcessing: true }));
    
    // Simulate processing
    setTimeout(() => {
      const output = processCommand(value);
      setState(prev => ({
        currentInput: '',
        lastOutput: output,
        isProcessing: false
      }));
    }, 100);
  }, [processCommand]);

  return (
    <Box flexDirection="column" padding={1}>
      <Header />
      
      <InputBox
        value={state.currentInput}
        onChange={handleInputChange}
        onSubmit={handleSubmit}
        placeholder="Type your message or @path/to/file"
      />

      <OutputBox
        content={state.lastOutput}
        isVisible={!!state.lastOutput}
      />

      {state.isProcessing && (
        <Box marginY={1}>
          <Text dimColor>Processing...</Text>
        </Box>
      )}
    </Box>
  );
};
