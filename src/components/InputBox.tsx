import React from 'react';
import { Box, Text } from 'ink';
import TextInput from 'ink-text-input';

interface InputBoxProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: (value: string) => void;
  placeholder?: string;
  label?: string;
}

export const InputBox: React.FC<InputBoxProps> = ({
  value,
  onChange,
  onSubmit,
  placeholder = 'Type your message or @path/to/file',
  label = 'Input'
}) => {
  return (
    <Box flexDirection="column" marginY={1}>
      <Box borderStyle="round" borderColor="cyan" paddingX={1} paddingY={0}>
        <Box flexDirection="column" width="100%">
          <Text color="cyan" bold>
            ─ {label} {'─'.repeat(Math.max(0, 70 - label.length - 3))}
          </Text>
          <Box>
            <Text color="cyan">{'> '}</Text>
            <TextInput
              value={value}
              onChange={onChange}
              onSubmit={onSubmit}
              placeholder={placeholder}
            />
          </Box>
        </Box>
      </Box>
    </Box>
  );
};
