import React from 'react';
import { Box, Text } from 'ink';
import TextInput from 'ink-text-input';

interface InputBoxProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: (value: string) => void;
  placeholder?: string;
  label?: string;
}

export const InputBox: React.FC<InputBoxProps> = ({
  value,
  onChange,
  onSubmit,
  placeholder = 'Type your message or @path/to/file',
  label = 'Input'
}) => {
  return React.createElement(Box, { flexDirection: 'column', marginY: 1 },
    React.createElement(Box, { 
      borderStyle: 'round', 
      borderColor: 'cyan', 
      paddingX: 1, 
      paddingY: 0 
    },
      React.createElement(Box, { flexDirection: 'column', width: '100%' },
        React.createElement(Text, { color: 'cyan', bold: true },
          `─ ${label} ${'─'.repeat(Math.max(0, 70 - label.length - 3))}`
        ),
        React.createElement(Box, {},
          React.createElement(Text, { color: 'cyan' }, '> '),
          React.createElement(TextInput, {
            value,
            onChange,
            onSubmit,
            placeholder
          })
        )
      )
    )
  );
};
