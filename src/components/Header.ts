import React from 'react';
import { Box, Text } from 'ink';

interface HeaderProps {
  title?: string;
  subtitle?: string;
}

export const Header: React.FC<HeaderProps> = ({ 
  title = '🤖 AgentZero CLI', 
  subtitle = 'Press Ctrl+C to exit' 
}) => {
  return React.createElement(Box, { flexDirection: 'column', marginBottom: 1 },
    React.createElement(Text, { bold: true, color: 'cyan' }, title),
    React.createElement(Text, { dimColor: true }, subtitle)
  );
};
