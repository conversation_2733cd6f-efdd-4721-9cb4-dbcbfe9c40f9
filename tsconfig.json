{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "Node16", "moduleResolution": "Node16", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "jsx": "react-jsx", "declaration": true, "outDir": "dist", "rootDir": "src", "sourceMap": true, "strict": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "resolveJsonModule": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*", "src/index.ts"], "exclude": ["node_modules", "dist"]}