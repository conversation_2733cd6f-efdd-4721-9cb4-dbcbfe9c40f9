# AgentZero CLI

A minimal CLI boilerplate with Gemini CLI-style interface built using TypeScript and Node.js. This project provides a clean, rectangular input interface that stays within the main terminal window, just like Gemini CLI.

## Features

- 🎨 **Gemini CLI-style Interface**: Clean rectangular input box with clear borders that stays in terminal
- ⌨️ **Interactive Input**: Simple readline-based input with keyboard navigation
- 📝 **Formatted Output**: Colored output with clear processing indicators
- 🔄 **Real-time Processing**: Live feedback and response display
- 🎯 **TypeScript**: Full type safety with strict TypeScript configuration
- ⚡ **Minimal Stack**: Built with native Node.js readline and chalk for colors

## Quick Start

### Prerequisites

- Node.js 18.0.0 or higher
- npm, yarn, or pnpm

### Installation

1. Clone or download this repository
2. Install dependencies:

```bash
npm install
# or
yarn install
# or
pnpm install
```

### Running the Application

```bash
# Development mode (with TypeScript compilation)
npm run dev

# Or build and run
npm run build
npm start
```

### Usage

1. **Type your message** in the rectangular input box
2. **Press Enter** to submit your input
3. **View responses** displayed above the input box
4. **Type 'clear'** to clear the screen and reset
5. **Type 'exit' or 'quit'** or **Press Ctrl+C** to exit

## Project Structure

```
src/
├── index.tsx             # Main entry point with readline-based CLI
├── package.json          # Dependencies and scripts
├── tsconfig.json         # TypeScript configuration
└── README.md            # This file
```

## Architecture

### Main CLI (`src/index.tsx`)
- Simple readline-based interface
- Handles user input and command processing
- Creates Gemini CLI-style rectangular input boxes
- Manages terminal output with colored formatting
- Built-in commands: `clear`, `exit`, `quit`

## Customization

### Styling
The interface uses chalk for colors and Unicode box drawing characters. You can customize:
- Border colors and styles in the `createInputBox` function
- Text colors for different message types
- Box width and layout

### Functionality
The current implementation echoes user input. To add AI integration:

1. **Modify the input processing logic** in the main readline handler
2. **Replace the echo logic** with your AI service calls
3. **Add error handling** for API failures
4. **Implement async processing** as needed

### Example AI Integration

```typescript
rl.on('line', async (input: string) => {
  const trimmedInput = input.trim();

  if (!trimmedInput) {
    console.log(createInputBox());
    return;
  }

  try {
    console.log(chalk.green('\n✓ Processing:'), trimmedInput);

    // Call your AI service
    const response = await yourAIService.chat(trimmedInput);

    console.log(chalk.yellow('📤 Response:'), response + '\n');

  } catch (error) {
    console.log(chalk.red('❌ Error:'), error.message + '\n');
  }

  console.log(createInputBox());
});
```

## Scripts

- `npm run dev` - Run in development mode with TypeScript compilation
- `npm run build` - Compile TypeScript to JavaScript
- `npm run build:watch` - Watch mode compilation
- `npm run clean` - Remove compiled files
- `npm start` - Run the compiled application

## Technical Details

### TypeScript Configuration
- Strict type checking enabled
- ES2022 target with Node16 module resolution
- JSX support for React components
- Source maps for debugging

### Dependencies
- **chalk**: Terminal string styling and colors
- **readline**: Built-in Node.js module for interactive input
- **typescript**: Type checking and compilation
- **tsx**: Direct TypeScript execution for development

## Future Enhancements

This minimal boilerplate is designed for easy extension. Consider adding:

- **Configuration Management**: JSON/YAML config files for customization
- **Command History**: Arrow key navigation through previous inputs (using readline history)
- **File Operations**: File path input support with @path/to/file syntax
- **Themes**: Multiple color schemes and layout options
- **Commands**: Built-in command system for various operations
- **Streaming**: Real-time response streaming for AI services
- **Persistence**: Save conversation history to disk

## License

This project is provided as-is for educational and development purposes.
