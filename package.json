{"name": "agentzero", "version": "1.0.0", "type": "module", "description": "A minimal CLI boilerplate with Gemini CLI-style interface using Ink and TypeScript", "main": "dist/index.js", "bin": {"agentzero": "dist/index.js"}, "scripts": {"start": "tsx src/index.ts", "dev": "tsx src/index.ts", "build": "tsc", "build:watch": "tsc --watch", "clean": "rm -rf dist", "test": "echo \"No tests specified\" && exit 0"}, "keywords": ["cli", "tui", "terminal", "ink", "typescript", "gemini-style"], "engines": {"node": ">=18.0.0"}, "dependencies": {"@types/react": "^19.1.8", "chalk": "^5.4.1", "ink": "^6.0.1", "ink-spinner": "^5.0.0", "ink-text-input": "^6.0.0", "react": "^19.1.0"}, "devDependencies": {"@types/node": "^20.19.8", "ts-node": "^10.9.1", "tsx": "^4.6.2", "typescript": "^5.3.3"}}